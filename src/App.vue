<template>
  <div id="app">
    <el-container>
      <el-header class="header">
        <h1>热点看板</h1>
      </el-header>
      <el-main>
        <HotListBoard />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import HotListBoard from './components/HotListBoard.vue'

export default {
  name: 'App',
  components: {
    HotListBoard
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  margin: 0;
  padding: 0;
}

.header {
  background-color: #409eff;
  color: white;
  text-align: center;
  line-height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>